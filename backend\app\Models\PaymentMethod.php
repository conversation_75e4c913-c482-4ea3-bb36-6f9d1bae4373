<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $table = 'payment_methods';

    protected $fillable = [
        'type',
        'reference_number',
        'refer_type',
        'refer_id',
        'refer_name',
        'total',
        'payment_type',
        'cheque_no',
        'bank_name',
        'issue_date',
        'settled_amount',
        'balance_amount',
        'date',
    ];

    protected $casts = [
        'total' => 'decimal:2',
        'settled_amount' => 'decimal:2',
        'balance_amount' => 'decimal:2',
        'date' => 'date',
        'issue_date' => 'date',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'refer_id')->when($this->refer_type === 'Customer');
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'refer_id')->when($this->refer_type === 'Supplier');
    }
}