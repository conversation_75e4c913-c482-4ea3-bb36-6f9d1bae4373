import { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "react-toastify";
import { useAuth } from "../../context/NewAuthContext";
import { getApi } from "../../services/api";
import {
  FiSearch,
  FiRefreshCw,
  FiPlus,
  FiPlusCircle,
  FiTrash2,
} from "react-icons/fi";
import ItemForm from "../../components/Item Form/ItemForm";

const PurchaseInvoiceForm = ({
  onGenerateInvoice,
  onCancel,
  existingInvoice,
  createdAt,
}) => {
  const { user } = useAuth();
  const [invoice, setInvoice] = useState(
    existingInvoice || {
      billNumber: "",
      invoiceNumber: "PINV-" + Date.now(),
      purchaseDate: new Date().toISOString().split("T")[0],
      paymentMethod: "Cash",
      supplierId: "",
      storeId: "",
      paidAmount: 0,
      status: "paid", // Default to "paid" for Cash
      discountPercentage: 0,
      discountAmount: 0,
      discountAmountEdited: false,
      taxPercentage: 0,
      tax: 0,
      taxEdited: false,
      purchaseOrderId: "",
      // Add cheque-related fields
      chequeNo: "",
      bankName: "",
      issueDate: "",
      // Add bank account fields
      bankAccountId: "",
      bankAccountType: "",
    }
  );

  const [items, setItems] = useState(existingInvoice?.items || []);
  const [itemForm, setItemForm] = useState({
    itemId: "",
    searchQuery: "",
    quantity: 1,
    freeItems: 0,
    buyingCost: 0,
    discountPercentage: 0,
    discountAmount: 0,
    discountAmountEdited: false,
    // Add variant-specific fields
    variantId: null,
    batchNumber: null,
    expiryDate: null,
  });

  const [suppliers, setSuppliers] = useState([]);
  const [stores, setStores] = useState([]);
  const [products, setProducts] = useState([]);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showItemForm, setShowItemForm] = useState(false);
  const [showAddProductForm, setShowAddProductForm] = useState(false);
  const [paidAmountFocused, setPaidAmountFocused] = useState(false);
  const [discountAmountFocused, setDiscountAmountFocused] = useState(false);
  const [taxAmountFocused, setTaxAmountFocused] = useState(false);
  const [itemDiscountAmountFocused, setItemDiscountAmountFocused] = useState(false);
  const [buyingCostFocused, setBuyingCostFocused] = useState(false);
  const [bankAccounts, setBankAccounts] = useState([]);

  const searchRef = useRef(null);
  const searchInputRef = useRef(null);
  const quantityInputRef = useRef(null);
  const freeItemsInputRef = useRef(null);
  const buyingCostInputRef = useRef(null);
  const discountPercentageInputRef = useRef(null);
  const discountAmountInputRef = useRef(null);
  const itemDiscountPercentageInputRef = useRef(null);
  const itemDiscountAmountInputRef = useRef(null);
  const taxPercentageInputRef = useRef(null);
  const taxInputRef = useRef(null);
  const paidAmountInputRef = useRef(null);
  const supplierSelectRef = useRef(null);
  const storeSelectRef = useRef(null);
  const generateInvoiceButtonRef = useRef(null);
  const purchaseOrderSelectRef = useRef(null);
  const paymentMethodSelectRef = useRef(null);
  const billNumberInputRef = useRef(null);
  const chequeNoInputRef = useRef(null);
  const bankNameInputRef = useRef(null);
  const issueDateInputRef = useRef(null);
  const bankAccountSelectRef = useRef(null);

  const api = getApi();

  // Add fetchData function to fix ReferenceError
  const fetchData = async () => {
    setLoading(true);
    try {
      const timestamp = new Date().getTime();
      const [suppliersRes, storesRes, productsRes, purchaseOrdersRes, bankAccountsRes] =
        await Promise.all([
          api.get(`/api/suppliers?_t=${timestamp}`),
          api.get(`/api/store-locations?_t=${timestamp}`),
          api.get(`/api/products?_t=${timestamp}`),
          api.get(`/api/purchase-orders?_t=${timestamp}`),
          api.get(`/api/staff-ledger/bank-accounts?_t=${timestamp}`),
        ]);

      const suppliersData = Array.isArray(suppliersRes.data.data)
        ? suppliersRes.data.data
        : Array.isArray(suppliersRes.data)
          ? suppliersRes.data
          : [];
      const storesData = Array.isArray(storesRes.data.data)
        ? storesRes.data.data
        : Array.isArray(storesRes.data)
          ? storesRes.data
          : [];
      const productsData = Array.isArray(productsRes.data.data)
        ? productsRes.data.data
        : Array.isArray(productsRes.data)
          ? productsRes.data
          : [];
      const purchaseOrdersData = Array.isArray(purchaseOrdersRes.data.data)
        ? purchaseOrdersRes.data.data
        : Array.isArray(purchaseOrdersRes.data)
          ? purchaseOrdersRes.data
          : [];
      const bankAccountsData = Array.isArray(bankAccountsRes.data.data)
        ? bankAccountsRes.data.data
        : Array.isArray(bankAccountsRes.data)
          ? bankAccountsRes.data
          : [];

      // Process products to include variants for batch-wise selection
      const processedProducts = productsData.map((p) => ({
        ...p,
        stock: parseFloat(p.opening_stock_quantity || 0),
        category_name: p.category || p.category_name || "Unknown",
        sales_price: parseFloat(p.sales_price || 0),
        mrp: parseFloat(p.mrp || 0),
        buying_cost: parseFloat(p.buying_cost || 0),
        supplier: p.supplier || "N/A",
        store_location: p.store_location || "N/A",
        // Include variants for batch selection
        variants: p.variants || [],
      }));

      setSuppliers(suppliersData);
      setStores(storesData);
      setProducts(processedProducts);
      setPurchaseOrders(purchaseOrdersData);
      setBankAccounts(bankAccountsData);

      if (suppliersData.length === 0) {
        setErrors((prev) => ({
          ...prev,
          suppliers:
            "No suppliers available. Please add suppliers in the system.",
        }));
        toast.warn("No suppliers available");
      }
      if (storesData.length === 0) {
        setErrors((prev) => ({
          ...prev,
          stores: "No stores available. Please add stores in the system.",
        }));
        toast.warn("No stores available");
      }
      if (productsData.length === 0) {
        setErrors((prev) => ({
          ...prev,
          products: "No products available. Please add products in the system.",
        }));
        toast.warn("No products available");
      }
      if (purchaseOrdersData.length === 0) {
        setErrors((prev) => ({
          ...prev,
          purchaseOrders:
            "No purchase orders available. Please create a purchase order first.",
        }));
        toast.warn("No purchase orders available");
      }
    } catch (error) {
      const errorMsg = error.response?.data?.message || "Error fetching data";
      setErrors({ fetch: errorMsg });
      toast.error(errorMsg);
      if (error.response?.status === 401) {
        toast.error("Session expired. Please login again.");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    purchaseOrderSelectRef.current?.focus();
  }, []);

  useEffect(() => {
    if (user?.token) {
      fetchData();
    } else {
      toast.error("Please login to access this form");
      setErrors({ auth: "User not authenticated" });
    }
  }, [user]);

  useEffect(() => {
    if (invoice.purchaseOrderId && !existingInvoice) {
      const selectedOrder = purchaseOrders.find(
        (order) => order.id === parseInt(invoice.purchaseOrderId)
      );
      if (selectedOrder) {
        setInvoice((prev) => ({
          ...prev,
          supplierId: selectedOrder.supplier_id.toString(),
          purchaseOrderId: selectedOrder.id.toString(),
          billNumber: "",
          invoiceNumber: `PINV-${Date.now()}`,
          purchaseDate: new Date().toISOString().split("T")[0],
          paymentMethod: "Cash",
          paidAmount: 0,
          status: "paid", // Default to "paid" for new Cash payment
          discountPercentage: 0,
          discountAmount: 0,
          taxPercentage: 0,
          tax: 0,
        }));

        const newItems = selectedOrder.orderItems.map((item, index) => ({
          id: index + 1,
          productId: item.product_id,
          description: item.description,
          quantity: item.qty,
          freeItems: 0,
          buyingCost: parseFloat(item.unit_price) || 0,
          discountPercentage: 0,
          discountAmount: 0,
          subtotal: item.qty * parseFloat(item.unit_price),
          total: item.qty * parseFloat(item.unit_price),
          unit_type: item.product?.unit_type || "",
        }));
        setItems(newItems);
      }
    } else if (!invoice.purchaseOrderId && !existingInvoice) {
      setItems([]);
      setInvoice((prev) => ({
        ...prev,
        supplierId: "",
        billNumber: "",
        status: prev.paymentMethod === "Credit" ? "pending" : "paid",
        items: [],
      }));
    }
  }, [invoice.purchaseOrderId, purchaseOrders, existingInvoice]);

  useEffect(() => {
    // Update status based on payment method and paid amount
    const total = calculateFinalTotal();
    setInvoice((prev) => ({
      ...prev,
      status:
        ["Credit", "Cheque"].includes(prev.paymentMethod) && (prev.paidAmount < total || prev.paidAmount === 0)
          ? "pending"
          : ["Cash", "Card", "Online", "Cheque", "Credit"].includes(prev.paymentMethod) && prev.paidAmount >= total
            ? "paid"
            : prev.status,
    }));
  }, [
    invoice.paymentMethod,
    invoice.paidAmount,
    items,
    invoice.discountAmount,
    invoice.tax,
  ]);

  // Generate filtered product options with variants (similar to POS form)
  const getFilteredProductOptions = useCallback(() => {
    const options = [];
    products.forEach((product) => {
      if (product.variants && product.variants.length > 0) {
        // Product has variants - create option for each variant
        product.variants.forEach((variant) => {
          const categoryName = product.category_name || "Unknown";
          const stockInfo = variant.opening_stock_quantity || 0;
          const batchInfo = variant.batch_number
            ? ` (Batch: ${variant.batch_number})`
            : "";
          const expiryInfo = variant.expiry_date
            ? ` (Exp: ${variant.expiry_date.split("T")[0]})`
            : "";

          options.push({
            ...product,
            ...variant,
            // Use variant-specific values
            product_id: product.product_id,
            variant_id: variant.product_variant_id,
            display_name: `${product.product_name}${batchInfo}${expiryInfo}`,
            stock: parseFloat(stockInfo),
            buying_cost: parseFloat(
              variant.buying_cost || product.buying_cost || 0
            ),
            sales_price: parseFloat(variant.sales_price || 0),
            mrp: parseFloat(variant.mrp || 0),
            batch_number: variant.batch_number,
            expiry_date: variant.expiry_date,
            store_location:
              variant.store_location || product.store_location || "N/A",
            category_name: categoryName,
          });
        });
      } else {
        // No variants - add product as is
        const categoryName = product.category_name || "Unknown";
        const option = {
          ...product,
          display_name: product.product_name,
          stock: parseFloat(product.opening_stock_quantity || 0),
          category_name: categoryName,
          buying_cost: parseFloat(product.buying_cost || 0),
          sales_price: parseFloat(product.sales_price || 0),
          mrp: parseFloat(product.mrp || 0),
        };
        options.push(option);
      }
    });
    return options;
  }, [products]);

  useEffect(() => {
    if (itemForm.searchQuery.trim()) {
      const query = itemForm.searchQuery.toLowerCase();
      const productOptions = getFilteredProductOptions();
      const startsWithOnly = localStorage.getItem("productSearchStartsWithOnly") === "true";
      const filtered = productOptions.filter(
        (p) => {
          if (startsWithOnly) {
            return (
              p.product_name.toLowerCase().startsWith(query) ||
              p.display_name.toLowerCase().startsWith(query) ||
              p.item_code?.toLowerCase().startsWith(query) ||
              p.barcode?.toLowerCase().startsWith(query) ||
              p.batch_number?.toLowerCase().startsWith(query)
            );
          } else {
            return (
              p.product_name.toLowerCase().includes(query) ||
              p.display_name.toLowerCase().includes(query) ||
              p.item_code?.toLowerCase().includes(query) ||
              p.barcode?.toLowerCase().includes(query) ||
              p.batch_number?.toLowerCase().includes(query)
            );
          }
        }
      );
      setFilteredProducts(filtered);
      setShowSuggestions(true);
      // Initialize to first item if results exist, otherwise -1
      setHighlightedIndex(filtered.length > 0 ? 0 : -1);
    } else {
      setFilteredProducts([]);
      setShowSuggestions(false);
      setHighlightedIndex(-1);
    }
  }, [itemForm.searchQuery, products, getFilteredProductOptions]);

  // Remove this useEffect as it was overriding variant-specific buying costs
  // The buying cost is now correctly set in handleSelectProduct for each variant

  useEffect(() => {
    if (itemForm.discountPercentage > 0 && !itemForm.discountAmountEdited) {
      const itemSubtotal =
        (itemForm.quantity + itemForm.freeItems) * itemForm.buyingCost;
      setItemForm((prev) => ({
        ...prev,
        discountAmount: (itemSubtotal * itemForm.discountPercentage) / 100,
      }));
    }
  }, [
    itemForm.discountPercentage,
    itemForm.quantity,
    itemForm.freeItems,
    itemForm.buyingCost,
  ]);

  useEffect(() => {
    if (itemForm.discountAmount > 0 && itemForm.discountAmountEdited) {
      const itemSubtotal =
        (itemForm.quantity + itemForm.freeItems) * itemForm.buyingCost;
      const calculatedPercentage =
        itemSubtotal > 0 ? (itemForm.discountAmount / itemSubtotal) * 100 : 0;
      setItemForm((prev) => ({
        ...prev,
        discountPercentage: parseFloat(calculatedPercentage.toFixed(1)),
      }));
    }
  }, [itemForm.discountAmount]);

  useEffect(() => {
    if (invoice.discountPercentage > 0 && !invoice.discountAmountEdited) {
      const subtotal = calculateSubtotal();
      setInvoice((prev) => ({
        ...prev,
        discountAmount: (subtotal * invoice.discountPercentage) / 100,
      }));
    }
  }, [invoice.discountPercentage, items]);

  useEffect(() => {
    if (invoice.discountAmount > 0 && invoice.discountAmountEdited) {
      const subtotal = calculateSubtotal();
      const calculatedPercentage =
        subtotal > 0 ? (invoice.discountAmount / subtotal) * 100 : 0;
      setInvoice((prev) => ({
        ...prev,
        discountPercentage: parseFloat(calculatedPercentage.toFixed(1)),
      }));
    }
  }, [invoice.discountAmount, items]);

  useEffect(() => {
    if (invoice.taxPercentage > 0 && !invoice.taxEdited) {
      const subtotal = calculateSubtotal();
      const taxableAmount = subtotal - invoice.discountAmount;
      setInvoice((prev) => ({
        ...prev,
        tax: (taxableAmount * invoice.taxPercentage) / 100,
      }));
    }
  }, [invoice.taxPercentage, invoice.discountAmount, items]);

  useEffect(() => {
    if (invoice.tax > 0 && invoice.taxEdited) {
      const subtotal = calculateSubtotal();
      const taxableAmount = subtotal - invoice.discountAmount;
      const calculatedPercentage =
        taxableAmount > 0 ? (invoice.tax / taxableAmount) * 100 : 0;
      setInvoice((prev) => ({
        ...prev,
        taxPercentage: parseFloat(calculatedPercentage.toFixed(1)),
      }));
    }
  }, [invoice.tax, invoice.discountAmount, items]);

  const handleItemFormChange = (e) => {
    const { name, value } = e.target;
    setItemForm((prev) => ({
      ...prev,
      [name]:
        name === "searchQuery"
          ? value
          : name === "itemId"
            ? value
            : name === "discountAmount" || name === "buyingCost"
              ? parseFormattedNumber(value)
              : parseFloat(value) || 0,
      ...(name === "discountPercentage"
        ? { discountAmountEdited: false, discountAmount: 0 }
        : {}),
      ...(name === "discountAmount"
        ? { discountAmountEdited: true, discountPercentage: 0 }
        : {}),
    }));
  };

  const handleSelectProduct = (product) => {
    if (!product || !product.product_id) {
      console.error("Invalid product selected:", product);
      return;
    }

    // Debug logging to verify correct product selection
    console.log("Selected product:", {
      name: product.display_name || product.product_name,
      productId: product.product_id,
      variantId: product.variant_id,
      batchNumber: product.batch_number,
      expiryDate: product.expiry_date,
      buyingCost: product.buying_cost,
      parsedBuyingCost: parseFloat(product.buying_cost) || 0,
    });
    console.log(
      "Setting buying cost to:",
      parseFloat(product.buying_cost) || 0
    );

    setItemForm({
      ...itemForm,
      itemId: product.product_id.toString(),
      searchQuery: product.display_name || product.product_name,
      buyingCost: parseFloat(product.buying_cost) || 0,
      // Store variant information if available
      variantId: product.variant_id || null,
      batchNumber: product.batch_number || null,
      expiryDate: product.expiry_date || null,
    });
    setShowSuggestions(false);
    setHighlightedIndex(-1);
    quantityInputRef.current?.focus();
  };

  const handleSearchKeyDown = (e) => {
    const numSearchResults = filteredProducts.length;

    if (e.key === "ArrowDown") {
      if (numSearchResults > 0) {
        e.preventDefault();
        setHighlightedIndex((prev) => (prev + 1) % numSearchResults);
      }
    } else if (e.key === "ArrowUp") {
      if (numSearchResults > 0) {
        e.preventDefault();
        setHighlightedIndex(
          (prev) => (prev - 1 + numSearchResults) % numSearchResults
        );
      }
    } else if (e.key === "Enter") {
      e.preventDefault();
      if (numSearchResults > 0 && highlightedIndex >= 0) {
        handleSelectProduct(filteredProducts[highlightedIndex]);
      } else if (itemForm.itemId) {
        quantityInputRef.current?.focus();
      } else {
        paymentMethodSelectRef.current?.focus();
      }
    }
  };

  const handleQuantityKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      freeItemsInputRef.current?.focus();
    }
  };

  const handleFreeItemsKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      buyingCostInputRef.current?.focus();
    }
  };

  const handleBuyingCostKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      itemDiscountPercentageInputRef.current?.focus();
    }
  };

  const handleDiscountPercentageKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      itemDiscountAmountInputRef.current?.focus();
    }
  };

  const handleDiscountAmountKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addItem();
      searchInputRef.current?.focus();
    }
  };

  const handleInvoiceDiscountPercentageKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      taxPercentageInputRef.current?.focus();
    }
  };

  const handleInvoiceDiscountAmountKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      taxPercentageInputRef.current?.focus();
    }
  };

  const handleTaxPercentageKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      taxInputRef.current?.focus();
    }
  };

  const handleTaxKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      paidAmountInputRef.current?.focus();
    }
  };

  const handlePaidAmountKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      generateInvoiceButtonRef.current?.focus();
    }
  };

  const handlePurchaseOrderKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      billNumberInputRef.current?.focus();
    }
  };

  const handlePaymentMethodKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (["Online", "Card", "Cheque"].includes(invoice.paymentMethod)) {
        bankAccountSelectRef.current?.focus();
      } else {
        supplierSelectRef.current?.focus();
      }
    }
  };

  const handleSupplierKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      storeSelectRef.current?.focus();
    }
  };

  const handleStoreKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (invoice.purchaseOrderId) {
        generateInvoiceButtonRef.current?.focus();
      } else {
        searchInputRef.current?.focus();
      }
    }
  };

  const handleGenerateInvoiceKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      e.target.form?.requestSubmit();
    }
  };

  const handleGenerateBillNumberKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      paymentMethodSelectRef.current?.focus();
    }
  };

  // Helper function to format number with commas
  const formatNumberWithCommas = (num) => {
    if (num === null || num === undefined || num === '') return '';
    const number = parseFloat(num);
    if (isNaN(number)) return '';
    return number.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    });
  };

  // Helper function to parse formatted number
  const parseFormattedNumber = (str) => {
    if (!str) return 0;
    // Remove commas and parse
    const cleaned = str.toString().replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  };

  const handleInvoiceChange = (e) => {
    const { name, value } = e.target;
    setInvoice((prev) => {
      const newInvoice = {
        ...prev,
        [name]:
          name === "paidAmount" ||
          name === "discountPercentage" ||
          name === "discountAmount" ||
          name === "taxPercentage" ||
          name === "tax"
            ? parseFormattedNumber(value)
            : value,
        ...(name === "discountPercentage"
          ? { discountAmountEdited: false, discountAmount: 0 }
          : {}),
        ...(name === "discountAmount"
          ? { discountAmountEdited: true, discountPercentage: 0 }
          : {}),
        ...(name === "taxPercentage" ? { taxEdited: false, tax: 0 } : {}),
        ...(name === "tax" ? { taxEdited: true, taxPercentage: 0 } : {}),
      };
      // Update status based on payment method and paid amount
      const total = calculateFinalTotal();
      newInvoice.status =
        ["Credit", "Cheque"].includes(newInvoice.paymentMethod) && (newInvoice.paidAmount < total || newInvoice.paidAmount === 0)
          ? "pending"
          : ["Cash", "Card", "Online", "Cheque", "Credit"].includes(newInvoice.paymentMethod) && newInvoice.paidAmount >= total
            ? "paid"
            : newInvoice.status;
      return newInvoice;
    });
  };

  const addItem = () => {
    // Find the selected item from filtered products (which includes variants)
    const productOptions = getFilteredProductOptions();
    const selectedItem = productOptions.find(
      (p) =>
        p.product_id === parseInt(itemForm.itemId) &&
        (itemForm.variantId
          ? p.variant_id === itemForm.variantId
          : !p.variant_id)
    );

    if (!selectedItem) {
      setErrors({ item: "Please select a valid item." });
      toast.error("Please select a valid item.");
      return;
    }

    // Check if the same product (with same variant/batch) already exists
    const existingItemIndex = items.findIndex((item) => {
      // Convert to strings for comparison to handle different data types
      const itemProductId = String(item.productId || "");
      const selectedProductId = String(selectedItem.product_id || "");
      const itemVariantId = String(item.variantId || "");
      const selectedVariantId = String(itemForm.variantId || "");
      const itemBatchNumber = String(item.batchNumber || "");
      const selectedBatchNumber = String(itemForm.batchNumber || "");

      console.log("Comparing items:", {
        existing: {
          productId: itemProductId,
          variantId: itemVariantId,
          batchNumber: itemBatchNumber,
        },
        new: {
          productId: selectedProductId,
          variantId: selectedVariantId,
          batchNumber: selectedBatchNumber,
        },
      });

      // For products without variants, only compare product ID
      if (
        !selectedVariantId ||
        selectedVariantId === "null" ||
        selectedVariantId === ""
      ) {
        return itemProductId === selectedProductId;
      }

      // For products with variants, compare all three fields
      return (
        itemProductId === selectedProductId &&
        itemVariantId === selectedVariantId &&
        itemBatchNumber === selectedBatchNumber
      );
    });

    if (existingItemIndex !== -1) {
      // Update existing item by merging quantities
      setItems((prevItems) => {
        const updatedItems = [...prevItems];
        const existingItem = { ...updatedItems[existingItemIndex] };

        // Add quantities
        existingItem.quantity += itemForm.quantity;
        existingItem.freeItems += itemForm.freeItems;

        // Keep the existing buying cost and discount settings
        // Recalculate totals
        const totalQuantity = existingItem.quantity + existingItem.freeItems;
        existingItem.subtotal = totalQuantity * existingItem.buyingCost;
        existingItem.total =
          existingItem.subtotal - existingItem.discountAmount;

        updatedItems[existingItemIndex] = existingItem;
        return updatedItems;
      });

      // Show notification that quantities were merged
      console.log("Item quantities merged successfully");
      toast.success(
        `Quantities merged for ${selectedItem.product_name} (Qty: ${itemForm.quantity} added)`
      );
    } else {
      // Add new item
      const totalQuantity = itemForm.quantity + itemForm.freeItems;
      const subtotal = totalQuantity * itemForm.buyingCost;
      const total = subtotal - itemForm.discountAmount;

      const newItem = {
        id: items.length + 1,
        productId: selectedItem.product_id,
        description: selectedItem.product_name, // Use only product name, not display_name with batch info
        quantity: itemForm.quantity,
        freeItems: itemForm.freeItems,
        buyingCost: itemForm.buyingCost,
        discountPercentage: itemForm.discountPercentage,
        discountAmount: itemForm.discountAmount,
        subtotal,
        total,
        unit_type: selectedItem.unit_type || "",
        // Include variant information
        variantId: itemForm.variantId,
        batchNumber: itemForm.batchNumber,
        expiryDate: itemForm.expiryDate,
      };

      setItems([...items, newItem]);
      console.log("New item added:", newItem);
    }
    resetItemForm();
    searchInputRef.current?.focus();
  };

  const resetItemForm = () => {
    setItemForm({
      itemId: "",
      searchQuery: "",
      quantity: 1,
      freeItems: 0,
      buyingCost: 0,
      discountPercentage: 0,
      discountAmount: 0,
      discountAmountEdited: false,
      // Reset variant-specific fields
      variantId: null,
      batchNumber: null,
      expiryDate: null,
    });
    setShowSuggestions(false);
    setHighlightedIndex(-1);
    setErrors((prev) => ({ ...prev, item: undefined }));
  };

  const removeItem = (index) => {
    setItems(items.filter((_, idx) => idx !== index));
  };

  const updateItemField = (index, field, value) => {
    setItems((prevItems) => {
      const updatedItems = [...prevItems];
      const item = { ...updatedItems[index] };

      if (
        field === "quantity" ||
        field === "freeItems" ||
        field === "buyingCost" ||
        field === "discountPercentage" ||
        field === "discountAmount"
      ) {
        const numValue = parseFloat(value) || 0;
        item[field] = numValue;

        // Reset discount fields when one is changed
        if (field === "discountPercentage") {
          item.discountAmountEdited = false;
          item.discountAmount = 0;
        } else if (field === "discountAmount") {
          item.discountAmountEdited = true;
          item.discountPercentage = 0;
        }

        // Recalculate totals
        const totalQuantity = item.quantity + item.freeItems;
        item.subtotal = totalQuantity * item.buyingCost;
        item.total = item.subtotal - item.discountAmount;
      } else {
        item[field] = value;
      }

      updatedItems[index] = item;
      return updatedItems;
    });
  };

  const calculateItemSubtotal = () => {
    return items.reduce(
      (sum, item) => sum + item.quantity * item.buyingCost,
      0
    );
  };

  const calculateTotalItemDiscount = () => {
    return items.reduce((sum, item) => sum + item.discountAmount, 0);
  };

  const calculateSubtotal = () => {
    return calculateItemSubtotal() - calculateTotalItemDiscount();
  };

  const calculateFinalTotal = () => {
    const subtotal = calculateSubtotal();
    return subtotal - invoice.discountAmount + invoice.tax;
  };

  const calculateBalance = () => {
    return calculateFinalTotal() - (invoice.paidAmount || 0);
  };

  const validateForm = () => {
  const newErrors = {};
  const subtotal = calculateSubtotal();
  const total = calculateFinalTotal();

  if (!invoice.invoiceNumber)
    newErrors.invoiceNumber = "Invoice Number is required";
  if (!invoice.purchaseDate)
    newErrors.purchaseDate = "Purchase Date is required";
  if (!invoice.supplierId) newErrors.supplierId = "Supplier is required";
  if (!invoice.storeId) newErrors.storeId = "Store is required";
  if (items.length === 0) newErrors.items = "At least one item is required";
  if (invoice.paidAmount < 0)
    newErrors.paidAmount = "Paid amount cannot be negative";
  if (invoice.discountPercentage < 0)
    newErrors.discountPercentage =
      "Invoice discount percentage cannot be negative";
  if (invoice.discountAmount < 0)
    newErrors.discountAmount = "Invoice discount amount cannot be negative";
  if (invoice.taxPercentage < 0)
    newErrors.taxPercentage = "Tax percentage cannot be negative";
  if (invoice.tax < 0) newErrors.tax = "Tax cannot be negative";

  // Bank account validation for Online, Card, and Cheque payments
  if (["Online", "Card", "Cheque"].includes(invoice.paymentMethod)) {
    if (!invoice.bankAccountId) newErrors.bankAccountId = "Bank account is required";
  }

  // Cheque payment method validation
  if (invoice.paymentMethod === "Cheque") {
    if (!invoice.chequeNo) newErrors.chequeNo = "Cheque number is required";
    if (!invoice.bankName) newErrors.bankName = "Bank name is required";
    if (!invoice.issueDate) newErrors.issueDate = "Issue date is required";
    // Add validation to prevent paidAmount from exceeding total
    if (invoice.paidAmount > total) {
      newErrors.paidAmount = `Paid amount cannot exceed the total of LKR ${total.toFixed(2)} for Cheque payment`;
    }
  }

  // Payment method and status validation
  if (["Cash", "Card", "Online"].includes(invoice.paymentMethod)) {
    if (invoice.paidAmount < total) {
      newErrors.paidAmount = `Paid amount must be at least LKR ${total.toFixed(2)} for ${invoice.paymentMethod} payment to set status to 'paid'`;
    }
  } else if (invoice.paymentMethod === "Credit") {
    if (invoice.paidAmount >= total) {
      newErrors.paidAmount = `Paid amount must be less than LKR ${total.toFixed(2)} for Credit payment to set status to 'pending'`;
    }
  }

  items.forEach((item, index) => {
    if (item.discountPercentage < 0)
      newErrors[`item_${index}_discountPercentage`] =
        "Item discount percentage cannot be negative";
    if (item.discountAmount < 0)
      newErrors[`item_${index}_discountAmount`] =
        "Item discount amount cannot be negative";
  });

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    console.log("Items state before mapping:", items); // Debug log

    const newInvoice = {
      id: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      billNumber: invoice.billNumber || null,
      purchaseDate: invoice.purchaseDate,
      paymentMethod: invoice.paymentMethod,
      supplierId: invoice.supplierId,
      storeId: invoice.storeId,
      paidAmount: invoice.paidAmount,
      status: invoice.status,
      discountPercentage: invoice.discountPercentage,
      discountAmount: invoice.discountAmount,
      taxPercentage: invoice.taxPercentage,
      tax: invoice.tax,
      purchaseOrderId: invoice.purchaseOrderId,
      // Add cheque fields
      chequeNo: invoice.paymentMethod === "Cheque" ? invoice.chequeNo : null,
      bankName: invoice.paymentMethod === "Cheque" ? invoice.bankName : null,
      issueDate: invoice.paymentMethod === "Cheque" ? invoice.issueDate : null,
      // Add bank account fields
      bankAccountId: ["Online", "Card", "Cheque"].includes(invoice.paymentMethod) ? invoice.bankAccountId : null,
      bankAccountType: ["Online", "Card", "Cheque"].includes(invoice.paymentMethod) ? invoice.bankAccountType : null,
      items: items.map((item) => {
        console.log("Mapping item for backend:", item); // Debug log
        return {
          product_id: item.productId, // Product ID
          product_name: item.description, // Product name for lookup
          variant_id: item.variantId || null, // Same field name as SaleController
          batch_number: item.batchNumber || null, // Same field name as SaleController
          expiry_date: item.expiryDate || null, // Same field name as SaleController
          quantity: item.quantity,
          free_items: item.freeItems || 0,
          buying_cost: item.buyingCost, // Required field
          discount_percentage: item.discountPercentage || 0,
          discount_amount: item.discountAmount || 0,
        };
      }),
      total: calculateFinalTotal(),
    };

    try {
      await onGenerateInvoice(newInvoice);

      if (invoice.purchaseOrderId) {
        try {
          await api.delete(`/api/purchase-orders/${invoice.purchaseOrderId}`);
          toast.success(
            `Purchase order #${invoice.purchaseOrderId} deleted successfully`
          );
          const timestamp = new Date().getTime();
          const purchaseOrdersRes = await api.get(
            `/api/purchase-orders?_t=${timestamp}`
          );
          const purchaseOrdersData = Array.isArray(purchaseOrdersRes.data.data)
            ? purchaseOrdersRes.data.data
            : Array.isArray(purchaseOrdersRes.data)
              ? purchaseOrdersRes.data
              : [];
          setPurchaseOrders(purchaseOrdersData);
        } catch (error) {
          const errorMsg =
            error.response?.data?.message || "Error deleting purchase order";
          toast.error(errorMsg);
        }
      }
    } catch (error) {
      const errorMsg = error.response?.data?.message || "Error saving invoice";
      toast.error(errorMsg);
    }
  };

  const handleNewItemSubmit = async (itemData) => {
    try {
      const response = await api.post("/api/products", itemData);
      const newProduct = response.data.data;
      setProducts((prev) => [...prev, newProduct]);
      setItemForm((prev) => ({
        ...prev,
        itemId: newProduct.product_id.toString(),
        searchQuery: newProduct.product_name,
        buyingCost: parseFloat(newProduct.buying_cost) || 0,
      }));
      setShowItemForm(false);
      toast.success("New item created successfully");
      searchInputRef.current?.focus();
    } catch (error) {
      console.error("Error creating new item:", error);
      toast.error("Failed to create new item");
    }
  };

  useEffect(() => {
    // If editing and createdAt is available, set purchaseDate to createdAt
    if (existingInvoice && (existingInvoice.createdAt || createdAt)) {
      setInvoice((prev) => ({
        ...prev,
        purchaseDate: new Date(existingInvoice.createdAt || createdAt).toISOString().split("T")[0],
      }));
    }
    // eslint-disable-next-line
  }, [existingInvoice, createdAt]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center w-full h-full overflow-y-auto bg-opacity-50 bg-slate-400">
      {showItemForm && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative w-full max-w-lg p-6 bg-white rounded-lg shadow-xl dark:bg-gray-800 max-h-[90vh] overflow-y-auto">
            <ItemForm
              onSubmit={handleNewItemSubmit}
              onClose={() => setShowItemForm(false)}
            />
          </div>
        </div>
      )}
      {showAddProductForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-lg p-6 bg-white rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900">Add New Product</h2>
            <ItemForm
              onSubmit={handleNewItemSubmit}
              onClose={() => setShowAddProductForm(false)}
              initialData={null}
              isBatchOnly={false}
            />
          </div>
        </div>
      )}
      <div className="w-full h-full p-6 overflow-y-auto bg-white shadow-xl dark:bg-gray-800">
        <button
          onClick={onCancel}
          className="absolute text-gray-500 transition top-4 right-4 hover:text-red-500"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-6 h-6"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <h2 className="mb-6 text-2xl font-semibold text-gray-900 dark:text-gray-100">
          {existingInvoice
            ? "Edit Purchase Invoice"
            : "Create Purchase Invoice"}
        </h2>
        <div className="max-w-full p-4 mx-auto space-y-4 rounded-lg bg-gray-50 dark:bg-gray-700">
          {Object.keys(errors).length > 0 && (
            <div className="p-2 mb-4 text-red-800 bg-red-100 rounded dark:bg-red-900 dark:text-red-200">
              {Object.values(errors).filter(Boolean).join(", ")}
            </div>
          )}
          {loading && (
            <div className="flex items-center justify-center p-8">
              <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
            </div>
          )}
          {!loading && (
            <form
              onSubmit={handleSubmit}
              className="w-full h-full p-6 space-y-6 overflow-y-auto bg-slate-100 text-slate-900 dark:text-white dark:bg-gray-800"
            >
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div>
                  <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                    Purchase Order
                  </label>
                  <select
                    ref={purchaseOrderSelectRef}
                    name="purchaseOrderId"
                    value={invoice.purchaseOrderId}
                    onChange={handleInvoiceChange}
                    onKeyDown={handlePurchaseOrderKeyDown}
                    className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                    disabled={loading || existingInvoice}
                  >
                    <option value="">Select Purchase Order</option>
                    {purchaseOrders.map((order) => (
                      <option key={order.id} value={order.id}>
                        ORD{order.id} -{" "}
                        {order.supplier?.supplier_name || order.contact_name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                     Date
                  </label>
                  <input
                    type="date"
                    name="purchaseDate"
                    value={invoice.purchaseDate}
                    onChange={handleInvoiceChange}
                    className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={loading}
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                    Bill Number
                  </label>
                  <input
                    type="text"
                    ref={billNumberInputRef}
                    name="billNumber"
                    value={invoice.billNumber}
                    onChange={handleInvoiceChange}
                    onKeyDown={handleGenerateBillNumberKeyDown}
                    className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter bill number or leave blank to auto-generate"
                    disabled={loading}
                  />
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                    Payment Method
                  </label>
                  <select
                    ref={paymentMethodSelectRef}
                    name="paymentMethod"
                    value={invoice.paymentMethod}
                    onChange={handleInvoiceChange}
                    onKeyDown={handlePaymentMethodKeyDown}
                    className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                    disabled={loading}
                  >
                    <option value="Cash">Cash</option>
                    <option value="Credit">Credit</option>
                    <option value="Cheque">Cheque</option>
                    <option value="Card">Card</option>
                    <option value="Online">Online</option>
                  </select>
                </div>

                {/* Bank Account field - show for Online, Card, and Cheque payment methods */}
                {["Online", "Card", "Cheque"].includes(invoice.paymentMethod) && (
                  <div>
                    <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                      Bank Account *
                    </label>
                    <select
                      ref={bankAccountSelectRef}
                      name="bankAccountId"
                      value={invoice.bankAccountId}
                      onChange={(e) => {
                        const selectedAccount = bankAccounts.find(acc => acc.id.toString() === e.target.value);
                        setInvoice(prev => ({
                          ...prev,
                          bankAccountId: e.target.value,
                          bankAccountType: selectedAccount ? selectedAccount.type : ""
                        }));
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (invoice.paymentMethod === "Cheque") {
                            chequeNoInputRef.current?.focus();
                          } else {
                            supplierSelectRef.current?.focus();
                          }
                        }
                      }}
                      className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                      required={["Online", "Card", "Cheque"].includes(invoice.paymentMethod)}
                      disabled={loading}
                    >
                      <option value="">Select Bank Account</option>
                      {bankAccounts.map((account) => (
                        <option key={`${account.type}-${account.id}`} value={account.id}>
                          {account.name} ({account.account_group})
                        </option>
                      ))}
                    </select>
                    {errors.bankAccountId && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.bankAccountId}
                      </p>
                    )}
                  </div>
                )}

                {/* Cheque fields - show only when Cheque is selected */}
                {invoice.paymentMethod === "Cheque" && (
                  <>
                    <div>
                      <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                        Cheque Number *
                      </label>
                      <input
                        ref={chequeNoInputRef}
                        type="text"
                        name="chequeNo"
                        value={invoice.chequeNo}
                        onChange={handleInvoiceChange}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            bankNameInputRef.current?.focus();
                          }
                        }}
                        className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter cheque number"
                        required={invoice.paymentMethod === "Cheque"}
                        disabled={loading}
                      />
                    </div>
                    <div>
                      <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                        Bank Name *
                      </label>
                      <input
                        ref={bankNameInputRef}
                        type="text"
                        name="bankName"
                        value={invoice.bankName}
                        onChange={handleInvoiceChange}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            issueDateInputRef.current?.focus();
                          }
                        }}
                        className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter bank name"
                        required={invoice.paymentMethod === "Cheque"}
                        disabled={loading}
                      />
                    </div>
                    <div>
                      <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                        Issue Date *
                      </label>
                      <input
                        ref={issueDateInputRef}
                        type="date"
                        name="issueDate"
                        value={invoice.issueDate}
                        onChange={handleInvoiceChange}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            supplierSelectRef.current?.focus();
                          }
                        }}
                        className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                        required={invoice.paymentMethod === "Cheque"}
                        disabled={loading}
                      />
                    </div>
                  </>
                )}
                <div>
                  <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                    Supplier
                  </label>
                  <select
                    ref={supplierSelectRef}
                    name="supplierId"
                    value={invoice.supplierId}
                    onChange={handleInvoiceChange}
                    onKeyDown={handleSupplierKeyDown}
                    className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={loading || invoice.purchaseOrderId}
                  >
                    <option value="">Select Supplier</option>
                    {suppliers.map((s) => (
                      <option key={s.id} value={s.id}>
                        {s.supplier_name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block mb-1 text-sm font-medium text-slate-800 dark:text-gray-300">
                    Store
                  </label>
                  <select
                    ref={storeSelectRef}
                    name="storeId"
                    value={invoice.storeId}
                    onChange={handleInvoiceChange}
                    onKeyDown={handleStoreKeyDown}
                    className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={loading}
                  >
                    <option value="">Select Store</option>
                    {stores.map((s) => (
                      <option key={s.id} value={s.id}>
                        {s.store_name}
                      </option>
                    ))}
                  </select>
                </div>
                {existingInvoice && (
                  <div>
                    <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                      Status
                    </label>
                    <select
                      name="status"
                      value={invoice.status}
                      onChange={handleInvoiceChange}
                      className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-500"
                      disabled={loading}
                    >
                      <option value="pending">Pending</option>
                      <option value="paid">Paid</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                )}
              </div>

              <div className="p-6 bg-white border border-gray-100 shadow-sm rounded-xl dark:bg-gray-800 dark:border-gray-700">
                <div className="flex items-center justify-between mb-5">
                  <h3 className="flex items-center gap-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                    <FiPlusCircle className="text-blue-500" />
                    Add Item
                  </h3>
                  {/* <button
                    type="button"
                    onClick={() => setShowItemForm(true)}
                    className="px-3 py-1.5 text-sm text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500"
                  >
                    Create New Item
                  </button> */}
                </div>

                <div className="grid grid-cols-1 gap-5 md:grid-cols-7">
                  <div ref={searchRef} className="relative flex items-end gap-2 md:col-span-2">
                    <div className="flex-1">
                      <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        Search Item
                      </label>
                      <div className="relative">
                        <input
                          ref={searchInputRef}
                          type="text"
                          name="searchQuery"
                          value={itemForm.searchQuery}
                          onChange={handleItemFormChange}
                          onFocus={() =>
                            itemForm.searchQuery && setShowSuggestions(true)
                          }
                          onKeyDown={handleSearchKeyDown}
                          placeholder="Type to search products..."
                          className="w-full p-2.5 pl-10 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                          disabled={loading}
                        />
                        <FiSearch className="absolute text-gray-400 left-3 top-3.5" />
                      </div>
                      {showSuggestions && filteredProducts.length > 0 && (
                        <ul className="absolute z-10 w-full mt-1 overflow-y-auto bg-white border border-gray-200 divide-y divide-gray-200 rounded-lg shadow-lg dark:bg-gray-700 dark:border-gray-600 max-h-60 dark:divide-gray-600">
                          {filteredProducts.map((p, index) => (
                            <li
                              key={`${p.product_id}-${p.variant_id || "no-variant"}`}
                              onClick={() => handleSelectProduct(p)}
                              onMouseEnter={() => setHighlightedIndex(index)}
                              className={`px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors ${
                                highlightedIndex === index
                                  ? "bg-blue-50 dark:bg-gray-600"
                                  : ""
                              }`}
                            >
                              <div className="flex flex-col">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-gray-900 dark:text-white">
                                    {p.display_name || p.product_name}
                                  </span>
                                  <span className="px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-full dark:bg-gray-600 dark:text-gray-300">
                                    Stock: {p.stock || p.opening_stock_quantity || 0}
                                  </span>
                                </div>
                                {(p.batch_number || p.expiry_date) && (
                                  <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {p.batch_number && `Batch: ${p.batch_number}`}
                                    {p.batch_number && p.expiry_date && " | "}
                                    {p.expiry_date && `Exp: ${p.expiry_date.split("T")[0]}`}
                                  </div>
                                )}
                                <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                  Buying Cost: LKR {(p.buying_cost || 0).toFixed(2)}
                                  {p.category_name && ` | ${p.category_name}`}
                                </div>
                              </div>
                            </li>
                          ))}
                        </ul>
                      )}
                      {showSuggestions && itemForm.searchQuery && filteredProducts.length === 0 && (
                        <div className="absolute z-10 w-full p-3 text-gray-500 bg-white border border-gray-200 rounded-lg shadow-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                          No products found
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => setShowAddProductForm(true)}
                      className="h-[42px] px-3 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 flex items-center gap-1"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                      Add Product
                    </button>
                  </div>

                  <div>
                    <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      Quantity
                    </label>
                    <input
                      ref={quantityInputRef}
                      type="number"
                      name="quantity"
                      value={itemForm.quantity}
                      onChange={handleItemFormChange}
                      onKeyDown={handleQuantityKeyDown}
                      className="w-full p-2.5 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                      min="1"
                      step="1"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      Free Items
                    </label>
                    <input
                      ref={freeItemsInputRef}
                      type="number"
                      name="freeItems"
                      value={itemForm.freeItems}
                      onChange={handleItemFormChange}
                      onKeyDown={handleFreeItemsKeyDown}
                      className="w-full p-2.5 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                      min="0"
                      step="1"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      Buying Cost
                    </label>
                    <div className="relative">
                      <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400">
                        LKR
                      </span>
                      <input
                        ref={buyingCostInputRef}
                        type="text"
                        name="buyingCost"
                        value={buyingCostFocused ? itemForm.buyingCost : formatNumberWithCommas(itemForm.buyingCost)}
                        onChange={handleItemFormChange}
                        onFocus={() => setBuyingCostFocused(true)}
                        onBlur={() => setBuyingCostFocused(false)}
                        onKeyDown={handleBuyingCostKeyDown}
                        className="w-full p-2.5 pl-8 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                        placeholder="0.00"
                        disabled={loading}
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      Discount
                    </label>
                    <div className="flex gap-3">
                      <div className="relative flex-1">
                        <input
                          ref={itemDiscountPercentageInputRef}
                          type="number"
                          name="discountPercentage"
                          value={itemForm.discountPercentage}
                          onChange={handleItemFormChange}
                          onKeyDown={handleDiscountPercentageKeyDown}
                          className="w-full p-2.5 pr-8 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                          placeholder="0%"
                          min="0"
                          max="100"
                          step="0.1"
                          disabled={loading || !itemForm.itemId}
                        />
                        <span className="absolute text-gray-500 right-3 top-3 dark:text-gray-400">
                          %
                        </span>
                      </div>
                      <div className="relative flex-1">
                        <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400"></span>
                        <input
                          ref={itemDiscountAmountInputRef}
                          type="text"
                          name="discountAmount"
                          value={itemDiscountAmountFocused ? itemForm.discountAmount : formatNumberWithCommas(itemForm.discountAmount)}
                          onChange={handleItemFormChange}
                          onFocus={() => setItemDiscountAmountFocused(true)}
                          onBlur={() => setItemDiscountAmountFocused(false)}
                          onKeyDown={handleDiscountAmountKeyDown}
                          className="w-full p-2.5 pl-8 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                          placeholder="0.00"
                          disabled={loading || !itemForm.itemId}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex items-end gap-3">
                    <button
                      type="button"
                      tabIndex={0}
                      onClick={addItem}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addItem();
                          searchInputRef.current?.focus();
                        }
                      }}
                      className={`flex-1 px-4 py-2.5 text-white rounded-lg transition-all ${
                        loading || !itemForm.itemId || itemForm.quantity <= 0
                          ? "bg-blue-400 cursor-not-allowed"
                          : "bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-300"
                      } flex items-center justify-center gap-2`}
                      disabled={
                        loading || !itemForm.itemId || itemForm.quantity <= 0
                      }
                    >
                      <FiPlus /> Add
                    </button>
                    <button
                      type="button"
                      onClick={resetItemForm}
                      className="flex-1 px-4 py-2.5 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-300 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-700 transition-all flex items-center justify-center gap-2"
                      disabled={loading}
                    >
                      <FiRefreshCw size={14} /> Reset
                    </button>
                  </div>
                </div>
              </div>

              {items.length > 0 && (
                <div className="space-y-6">
                  <div className="overflow-hidden border border-gray-200 shadow-sm rounded-xl dark:border-gray-700">
                    <div className="overflow-auto max-h-96">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                              #
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase dark:text-gray-300">
                              Description
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase dark:text-gray-300">
                              Qty
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase dark:text-gray-300">
                              Free
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                              Unit Cost
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                              Total
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                              Disc. %
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                              Disc. Amt
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                              Subtotal
                            </th>
                            <th className="px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase dark:text-gray-300">
                              Action
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                          {items.map((item, index) => (
                            <tr
                              key={item.id}
                              className="transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                            >
                              <td className="px-4 py-3 text-sm text-gray-700 whitespace-nowrap dark:text-gray-300">
                                {index + 1}
                              </td>
                              <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                <div className="flex flex-col">
                                  <input
                                    type="text"
                                    value={item.description}
                                    onChange={(e) =>
                                      updateItemField(
                                        index,
                                        "description",
                                        e.target.value
                                      )
                                    }
                                    className="w-full p-1 text-sm font-medium bg-transparent border border-gray-300 rounded dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Item description"
                                  />
                                  {(item.batchNumber || item.expiryDate) && (
                                    <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                      {item.batchNumber &&
                                        `Batch: ${item.batchNumber}`}
                                      {item.batchNumber &&
                                        item.expiryDate &&
                                        " | "}
                                      {item.expiryDate &&
                                        `Exp: ${item.expiryDate.split("T")[0]}`}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-center text-gray-700 whitespace-nowrap dark:text-gray-300">
                                <input
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) =>
                                    updateItemField(
                                      index,
                                      "quantity",
                                      e.target.value
                                    )
                                  }
                                  className="w-16 p-1 text-sm text-center bg-transparent border border-gray-300 rounded dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  min="0"
                                  step="1"
                                />
                              </td>
                              <td className="px-4 py-3 text-sm text-center text-gray-700 whitespace-nowrap dark:text-gray-300">
                                <input
                                  type="number"
                                  value={item.freeItems}
                                  onChange={(e) =>
                                    updateItemField(
                                      index,
                                      "freeItems",
                                      e.target.value
                                    )
                                  }
                                  className="w-16 p-1 text-sm text-center bg-transparent border border-gray-300 rounded dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  min="0"
                                  step="1"
                                />
                              </td>
                              <td className="px-4 py-3 text-sm text-right text-gray-700 whitespace-nowrap dark:text-gray-300">
                                <input
                                  type="number"
                                  value={item.buyingCost.toFixed(2)}
                                  onChange={(e) =>
                                    updateItemField(
                                      index,
                                      "buyingCost",
                                      e.target.value
                                    )
                                  }
                                  className="w-20 p-1 text-sm text-right bg-transparent border border-gray-300 rounded dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  min="0"
                                  step="0.01"
                                />
                              </td>
                              <td className="px-4 py-3 text-sm text-right text-gray-700 whitespace-nowrap dark:text-gray-300">
                                LKR{" "}
                                {formatNumberWithCommas(item.quantity * item.buyingCost)}
                              </td>
                              <td className="px-4 py-3 text-sm text-right text-gray-700 whitespace-nowrap dark:text-gray-300">
                                <input
                                  type="number"
                                  value={item.discountPercentage.toFixed(1)}
                                  onChange={(e) =>
                                    updateItemField(
                                      index,
                                      "discountPercentage",
                                      e.target.value
                                    )
                                  }
                                  className="w-16 p-1 text-sm text-right bg-transparent border border-gray-300 rounded dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  min="0"
                                  max="100"
                                  step="0.1"
                                />
                              </td>
                              <td className="px-4 py-3 text-sm text-right text-gray-700 whitespace-nowrap dark:text-gray-300">
                                <input
                                  type="number"
                                  value={item.discountAmount.toFixed(2)}
                                  onChange={(e) =>
                                    updateItemField(
                                      index,
                                      "discountAmount",
                                      e.target.value
                                    )
                                  }
                                  className="w-20 p-1 text-sm text-right bg-transparent border border-gray-300 rounded dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                  min="0"
                                  step="0.01"
                                />
                              </td>
                              <td className="px-4 py-3 text-sm font-medium text-right text-gray-900 whitespace-nowrap dark:text-white">
                                LKR{" "}
                                {formatNumberWithCommas(
                                  item.quantity * item.buyingCost -
                                  item.discountAmount
                                )}
                              </td>
                              <td className="px-4 py-3 text-sm font-medium text-right whitespace-nowrap">
                                <button
                                  onClick={() => removeItem(index)}
                                  disabled={loading}
                                  className="p-1 text-red-500 transition-colors rounded-full hover:text-red-700 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-gray-600"
                                  title="Remove item"
                                >
                                  <FiTrash2 className="w-4 h-4" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    <div className="p-4 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                            Item Subtotal:
                          </span>
                          <span className="text-sm font-medium">
                            LKR {formatNumberWithCommas(calculateItemSubtotal())}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                            Total Discount:
                          </span>
                          <span className="text-sm font-medium text-red-500">
                            - LKR {formatNumberWithCommas(calculateTotalItemDiscount())}
                          </span>
                        </div>
                        <div className="flex justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                          <span className="text-base font-semibold text-gray-700 dark:text-gray-200">
                            Subtotal:
                          </span>
                          <span className="text-base font-semibold">
                            LKR {formatNumberWithCommas(calculateSubtotal())}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Invoice Discount
                        </label>
                        <div className="flex gap-3">
                          <div className="relative flex-1">
                            <input
                              ref={discountPercentageInputRef}
                              type="number"
                              name="discountPercentage"
                              value={invoice.discountPercentage.toFixed(1)}
                              onChange={handleInvoiceChange}
                              onKeyDown={handleInvoiceDiscountPercentageKeyDown}
                              className="w-full p-2.5 pl-8 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                              placeholder="0%"
                              min="0"
                              max="100"
                              step="0.1"
                              disabled={calculateSubtotal() === 0}
                            />
                            <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400">
                              %
                            </span>
                          </div>
                          <div className="relative flex-1">
                            <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400">
                              LKR
                            </span>
                            <input
                              type="text"
                              name="discountAmount"
                              value={discountAmountFocused ? invoice.discountAmount : formatNumberWithCommas(invoice.discountAmount)}
                              onChange={handleInvoiceChange}
                              onFocus={() => setDiscountAmountFocused(true)}
                              onBlur={() => setDiscountAmountFocused(false)}
                              onKeyDown={handleInvoiceDiscountAmountKeyDown}
                              className="w-full p-2.5 pl-10 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                              placeholder="0.00"
                              disabled={calculateSubtotal() === 0}
                            />
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Tax
                        </label>
                        <div className="flex gap-3">
                          <div className="relative flex-1">
                            <input
                              ref={taxPercentageInputRef}
                              type="number"
                              name="taxPercentage"
                              value={invoice.taxPercentage.toFixed(1)}
                              onChange={handleInvoiceChange}
                              onKeyDown={handleTaxPercentageKeyDown}
                              className="w-full p-2.5 pl-8 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                              placeholder="0%"
                              min="0"
                              step="0.1"
                              disabled={calculateSubtotal() === 0}
                            />
                            <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400">
                              %
                            </span>
                          </div>
                          <div className="relative flex-1">
                            <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400">
                              LKR
                            </span>
                            <input
                              ref={taxInputRef}
                              type="text"
                              name="tax"
                              value={taxAmountFocused ? invoice.tax : formatNumberWithCommas(invoice.tax)}
                              onChange={handleInvoiceChange}
                              onFocus={() => setTaxAmountFocused(true)}
                              onBlur={() => setTaxAmountFocused(false)}
                              onKeyDown={handleTaxKeyDown}
                              className="w-full p-2.5 pl-10 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all"
                              placeholder="0.00"
                              disabled={calculateSubtotal() === 0}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                          Paid Amount
                        </label>
                        <div className="relative">
                          <span className="absolute text-gray-500 left-3 top-3 dark:text-gray-400">
                            LKR
                          </span>
                          <input
                            ref={paidAmountInputRef}
                            type="text"
                            name="paidAmount"
                            value={paidAmountFocused ? invoice.paidAmount : formatNumberWithCommas(invoice.paidAmount)}
                            onChange={handleInvoiceChange}
                            onFocus={() => setPaidAmountFocused(true)}
                            onBlur={() => setPaidAmountFocused(false)}
                            onKeyDown={handlePaidAmountKeyDown}
                            className={`w-full p-2.5 pl-10 border rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-500 transition-all ${
                              errors.paidAmount
                                ? "border-red-500"
                                : "border-gray-300"
                            }`}
                            placeholder="0.00"
                            disabled={loading}
                          />
                        </div>
                        {errors.paidAmount && (
                          <p className="mt-1 text-xs text-red-500">
                            {errors.paidAmount}
                          </p>
                        )}
                      </div>

                      <div className="p-4 space-y-2 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Total:
                          </span>
                          <span className="text-sm font-semibold">
                            LKR {formatNumberWithCommas(calculateFinalTotal())}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Paid:
                          </span>
                          <span className="text-sm font-semibold text-green-500">
                            LKR {formatNumberWithCommas(invoice.paidAmount)}
                          </span>
                        </div>
                        <div className="flex justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
                          <span className="text-base font-medium text-gray-700 dark:text-gray-300">
                            Balance:
                          </span>
                          <span
                            className={`text-base font-semibold ${
                              calculateBalance() < 0
                                ? "text-red-500"
                                : "text-blue-500"
                            }`}
                          >
                            LKR {formatNumberWithCommas(calculateBalance())}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={onCancel}
                  className="px-4 py-2 text-white transition bg-gray-500 rounded-lg hover:bg-gray-600"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  ref={generateInvoiceButtonRef}
                  type="submit"
                  onKeyDown={handleGenerateInvoiceKeyDown}
                  className="px-4 py-2 text-white transition bg-green-500 rounded-lg hover:bg-green-600"
                  disabled={loading || items.length === 0}
                >
                  {existingInvoice ? "Update Invoice" : "Generate Invoice"}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default PurchaseInvoiceForm;
